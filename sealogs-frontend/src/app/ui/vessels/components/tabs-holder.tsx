'use client'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import InfoTab from './tabs/info'
import MaintenanceTab from './tabs/maintenance'
import LogbookTabs from './tabs/logbook'
import CrewTab from './tabs/crew'
import CrewTrainingTab from './tabs/crew-training'
import InventoryTab from './tabs/inventory'
import DocumentsTab from './tabs/documents'
import { useQueryState } from 'nuqs'
import { Badge } from '@/components/ui'
import { useState } from 'react'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'

export default function TabsHolder({
    taskCounter,
    trainingDueCounter,
    vessel,
    engineList,
    fuelTankList,
    waterTankList,
    sewageSystemList,
    vesselId,
    logbooks,
    totalEntries,
    perPage,
    handlePagination,
    currentPage,
    maintenanceTasks,
    crewInfo,
    trainingSessions,
    trainingSessionDues,
    inventories,
    imCrew,
    edit_docs,
    setDocuments,
    documents,
    delete_docs,
    deleteFile,
}: {
    taskCounter: number
    trainingDueCounter: number
    vessel: any
    engineList: any
    fuelTankList: any
    waterTankList: any
    sewageSystemList: any
    vesselId: number
    logbooks: any
    totalEntries: any
    perPage: any
    handlePagination: any
    currentPage: any
    maintenanceTasks: any
    crewInfo: any
    trainingSessions: any
    trainingSessionDues: any
    inventories: any
    imCrew: any
    edit_docs: any
    setDocuments: any
    documents: any
    delete_docs: any
    deleteFile: any
}) {
    // Use nuqs to manage the tab state through URL query parameters
    const [tab, setTab] = useQueryState('tab', {
        defaultValue: 'logEntries',
    })
    const [permissions, setPermissions] = useState(getPermissions)
    const [isViewTask, setIsViewTask] = useState(
        hasPermission('VIEW_TASK', permissions),
    )
    return (
        <div className="my-4 w-full">
            <Tabs value={tab} onValueChange={setTab}>
                <TabsList>
                    <TabsTrigger value="info">Info</TabsTrigger>
                    <TabsTrigger value="logEntries">Log entries</TabsTrigger>
                    <TabsTrigger value="maintenance">
                        Maintenance
                        {taskCounter > 0 && (
                            <Badge
                                className="size-6 ml-2.5"
                                variant="destructive">
                                {taskCounter}
                            </Badge>
                        )}
                    </TabsTrigger>
                    <TabsTrigger value="crew">Crew</TabsTrigger>
                    <TabsTrigger value="crew_training">
                        Crew training{' '}
                        {trainingDueCounter > 0 && (
                            <Badge
                                className="size-6 ml-2.5"
                                variant="destructive">
                                {trainingDueCounter}
                            </Badge>
                        )}
                    </TabsTrigger>
                    <TabsTrigger value="inventory">Inventory</TabsTrigger>
                    <TabsTrigger value="documents">Documents</TabsTrigger>
                </TabsList>
                {/* {addButton} */}
                <TabsContent value="info" className="bg-background rounded-lg">
                    <InfoTab
                        vessel={vessel}
                        engineList={engineList}
                        fuelTankList={fuelTankList}
                        waterTankList={waterTankList}
                        sewageSystemList={sewageSystemList}
                    />
                </TabsContent>
                <TabsContent value="logEntries">
                    <LogbookTabs
                        vesselId={vesselId}
                        logbooks={logbooks}
                        totalEntries={totalEntries}
                        perPage={perPage}
                        handlePagination={handlePagination}
                        currentPage={currentPage}
                    />
                </TabsContent>
                <TabsContent value="maintenance">
                    {isViewTask ? (
                        <MaintenanceTab
                            maintenanceTasks={maintenanceTasks}
                            crewInfo={crewInfo}
                        />
                    ) : (
                        <Loading errorMessage="Oops! You don't have permission to view this section." />
                    )}
                </TabsContent>
                <TabsContent value="crew">
                    <CrewTab crewInfo={crewInfo} vessel={vessel} />
                </TabsContent>
                <TabsContent value="crew_training">
                    <CrewTrainingTab
                        trainingSessions={trainingSessions}
                        trainingSessionDues={trainingSessionDues}
                    />
                </TabsContent>
                <TabsContent value="inventory">
                    <InventoryTab inventories={inventories} />
                </TabsContent>
                <TabsContent value="documents">
                    <DocumentsTab
                        imCrew={imCrew}
                        edit_docs={edit_docs}
                        setDocuments={setDocuments}
                        documents={documents}
                        delete_docs={delete_docs}
                        deleteFile={deleteFile}
                    />
                </TabsContent>
            </Tabs>
        </div>
    )
}
